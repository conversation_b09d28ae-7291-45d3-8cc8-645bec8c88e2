# Installation Guide

## Development Installation (Recommended for Testing)

### Prerequisites
- Google Chrome browser (or Chromium-based browser like Edge)
- The extension files (this directory)

### Steps

1. **Download/Clone the Extension**
   - If you have the code, ensure all files are in a single directory
   - The directory should contain `manifest.json` at the root level

2. **Open Chrome Extensions Page**
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Or go to Menu → More Tools → Extensions

3. **Enable Developer Mode**
   - In the top-right corner, toggle "Developer mode" ON
   - This will show additional options

4. **Load the Extension**
   - Click "Load unpacked" button
   - Select the directory containing the extension files
   - The extension should appear in your extensions list

5. **Verify Installation**
   - You should see "Marketplace Deal Comparator" in your extensions
   - The extension icon should appear in your browser toolbar
   - Navigate to Facebook Marketplace to test functionality

### Testing the Extension

1. **Go to Facebook Marketplace**
   - Navigate to `https://www.facebook.com/marketplace`
   - Browse any category or search for items

2. **Test Selection**
   - Look for + buttons on listing items
   - Click to select/deselect items
   - Selected items should show a checkmark and highlight

3. **Test Side Panel**
   - Click the 📋 button (floating or in toolbar)
   - Side panel should slide out showing selected items
   - Test remove and clear functions

4. **Test Export**
   - Select a few items
   - Click export button in side panel or popup
   - CSV file should download automatically

## Troubleshooting

### Extension Not Loading
- **Check manifest.json**: Ensure the file exists and is valid JSON
- **Check file permissions**: Ensure Chrome can read the directory
- **Check console**: Look for errors in Chrome DevTools

### Extension Not Working on Facebook
- **Check URL**: Ensure you're on facebook.com/marketplace
- **Refresh page**: Try refreshing after installing
- **Check permissions**: Extension needs access to facebook.com

### Toggle Buttons Not Appearing
- **Wait for page load**: Facebook loads content dynamically
- **Try scrolling**: New content may trigger button creation
- **Check console**: Look for JavaScript errors

### Export Not Working
- **Check downloads**: Ensure Chrome can download files
- **Try popup export**: Use extension popup as fallback
- **Check selected items**: Ensure you have items selected

## Development Setup

If you want to modify the extension:

1. **Make Changes**
   - Edit any of the JavaScript, CSS, or HTML files
   - Changes to content scripts require page refresh
   - Changes to popup require extension reload

2. **Reload Extension**
   - Go to `chrome://extensions/`
   - Find your extension
   - Click the reload button (🔄)

3. **Test Changes**
   - Refresh any Facebook Marketplace tabs
   - Test the modified functionality

## Production Installation (Future)

Once published to Chrome Web Store:

1. Visit the Chrome Web Store
2. Search for "Marketplace Deal Comparator"
3. Click "Add to Chrome"
4. Confirm installation

## Uninstalling

To remove the extension:

1. Go to `chrome://extensions/`
2. Find "Marketplace Deal Comparator"
3. Click "Remove"
4. Confirm removal

All stored data will be deleted when uninstalling.

## Support

If you encounter issues:

1. Check this troubleshooting guide
2. Look at browser console for errors
3. Try disabling other extensions temporarily
4. Report issues with detailed steps to reproduce
