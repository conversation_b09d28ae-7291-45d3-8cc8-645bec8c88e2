# Marketplace Deal Comparator

A Chrome extension for comparing Facebook Marketplace listings. Select multiple items, extract their data, and export to CSV for analysis.

## Features

### Phase 1 (Current)
- ✅ Toggle button selection on marketplace listings
- ✅ Data extraction (title, price, location, seller, images, etc.)
- ✅ Collapsible side panel to view selected items
- ✅ CSV export functionality
- ✅ Persistent storage across browser sessions
- ✅ Works on both search results and individual listing pages

### Planned Features (Phase 2 & 3)
- Smart field detection and category-specific parsing
- Bottom bar interface
- Duplicate detection
- Advanced export options
- Price tracking

## Installation

### Development Installation
1. Clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension directory
5. Navigate to Facebook Marketplace to start using

### Production Installation
*Coming soon to Chrome Web Store*

## Usage

1. **Browse** Facebook Marketplace search results or individual listings
2. **Select** items by clicking the **+** toggle button that appears on listings
3. **View** selected items in the side panel (click the 📋 button or use the extension popup)
4. **Export** your comparison data to CSV for analysis
5. **Manage** selections (remove individual items or clear all)

## File Structure

```
marketplace-comparator/
├── manifest.json                 # Extension manifest
├── popup/                       # Extension popup interface
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── content/                     # Content scripts and UI
│   ├── content.js              # Main content script
│   ├── content.css             # Styles
│   └── ui-components/
│       ├── toggle-button.js    # Selection toggle buttons
│       └── side-panel.js       # Side panel component
├── background/                  # Background script
│   └── background.js
├── utils/                      # Utility modules
│   ├── storage-manager.js      # Chrome storage management
│   ├── data-extractor.js       # Data extraction from listings
│   └── csv-exporter.js         # CSV export functionality
└── assets/                     # Icons and static assets
    └── icons/
```

## Data Extracted

The extension extracts the following data from each listing:

- **Basic Info**: Title, Price, Currency, Location, Seller
- **Smart Detection**: Condition, Brand, Model, Specifications
- **Metadata**: URL, Images, Date Posted, Date Selected
- **Category**: Auto-detected product category
- **Raw Data**: Full text for custom analysis

## CSV Export Format

```csv
Title,Price,Currency,Price_Numeric,Location,Seller,Condition,Brand,Model,Specifications,Description,Images,URL,Date_Posted,Date_Selected,Category,Type,Extracted_At
```

## Privacy & Security

- **Local Storage Only**: All data is stored locally in your browser
- **No External Servers**: No data is sent to external servers
- **Facebook ToS Compliant**: Respects Facebook's terms of service
- **Minimal Permissions**: Only requests necessary permissions

## Development

### Prerequisites
- Chrome browser
- Basic knowledge of JavaScript and Chrome Extensions

### Setup
1. Clone the repository
2. Make changes to the code
3. Reload the extension in `chrome://extensions/`
4. Test on Facebook Marketplace

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Troubleshooting

### Extension Not Working
- Ensure you're on a Facebook Marketplace page
- Check that the extension is enabled in `chrome://extensions/`
- Try refreshing the page

### Toggle Buttons Not Appearing
- Facebook may have updated their page structure
- Try refreshing the page
- Check browser console for errors

### Export Not Working
- Ensure you have items selected
- Check browser's download settings
- Try using the popup export as fallback

## Technical Details

- **Manifest Version**: 3
- **Permissions**: `storage`, `activeTab`
- **Host Permissions**: `*://www.facebook.com/*`
- **Storage**: Chrome local storage (up to 5MB)
- **Compatibility**: Chrome, Edge, other Chromium browsers

## License

MIT License - see LICENSE file for details

## Support

- **Issues**: [GitHub Issues](https://github.com/your-repo/marketplace-comparator/issues)
- **Documentation**: This README
- **Updates**: Check Chrome Web Store or GitHub releases

## Version History

### v1.0.0 (Current)
- Initial release with core functionality
- Toggle selection, side panel, CSV export
- Persistent storage and cross-session support
