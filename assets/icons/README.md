# Icons

This directory should contain the extension icons in the following sizes:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Creating Icons

You can create icons using any image editor. The icon should represent the extension's purpose - comparing marketplace listings.

Suggested design elements:
- Shopping/marketplace theme
- Comparison/analysis symbols
- Clean, recognizable at small sizes

## Temporary Solution

For development, you can:
1. Create simple colored squares as placeholders
2. Use online icon generators
3. Use existing icons from icon libraries (with proper licensing)

The extension will work without icons, but they improve the user experience.
