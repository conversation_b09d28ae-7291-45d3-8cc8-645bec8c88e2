/**
 * Background Script - Handles extension lifecycle and cross-tab communication
 */

// Extension installation/update handler
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Marketplace Comparator installed/updated:', details.reason);
  
  if (details.reason === 'install') {
    // First time installation
    console.log('First time installation');
    
    // Optionally open welcome page or instructions
    // chrome.tabs.create({ url: 'popup/welcome.html' });
  }
});

// Handle messages from content scripts or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'getStatus':
      handleGetStatus(sendResponse);
      break;
      
    case 'clearAllData':
      handleClearAllData(sendResponse);
      break;
      
    case 'exportData':
      handleExportData(request.data, sendResponse);
      break;
      
    case 'getStorageUsage':
      handleGetStorageUsage(sendResponse);
      break;
      
    default:
      sendResponse({ error: 'Unknown action' });
  }
  
  // Return true to indicate we'll send a response asynchronously
  return true;
});

/**
 * Handle status request
 */
async function handleGetStatus(sendResponse) {
  try {
    const storage = await chrome.storage.local.get(['marketplace_selected_items']);
    const items = storage.marketplace_selected_items || [];
    
    sendResponse({
      success: true,
      data: {
        itemCount: items.length,
        lastUpdated: items.length > 0 ? items[0].dateSelected : null
      }
    });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle clear all data request
 */
async function handleClearAllData(sendResponse) {
  try {
    await chrome.storage.local.clear();
    sendResponse({ success: true });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle export data request
 */
async function handleExportData(data, sendResponse) {
  try {
    // In a background script, we can't directly download files
    // This would need to be handled by the content script or popup
    sendResponse({ 
      success: false, 
      error: 'Export must be handled by content script' 
    });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle storage usage request
 */
async function handleGetStorageUsage(sendResponse) {
  try {
    const usage = await chrome.storage.local.getBytesInUse();
    const quota = chrome.storage.local.QUOTA_BYTES;
    
    sendResponse({
      success: true,
      data: {
        used: usage,
        quota: quota,
        percentage: (usage / quota) * 100
      }
    });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Handle tab updates to refresh content scripts if needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('facebook.com/marketplace')) {
    
    // Optionally inject content script if it's not already there
    // This is mainly for development/debugging
    console.log('Marketplace page loaded:', tab.url);
  }
});

// Handle storage changes to sync across tabs
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.marketplace_selected_items) {
    // Notify all marketplace tabs about storage changes
    chrome.tabs.query({ url: '*://www.facebook.com/marketplace/*' }, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          action: 'storageChanged',
          changes: changes
        }).catch(() => {
          // Ignore errors for tabs that don't have content script
        });
      });
    });
  }
});

// Cleanup on extension shutdown
chrome.runtime.onSuspend.addListener(() => {
  console.log('Marketplace Comparator suspending');
});

// Handle extension icon click (if no popup is defined)
chrome.action.onClicked.addListener((tab) => {
  if (tab.url && tab.url.includes('facebook.com/marketplace')) {
    // Extension is active on marketplace pages
    chrome.tabs.sendMessage(tab.id, { action: 'togglePanel' });
  } else {
    // Redirect to marketplace if not already there
    chrome.tabs.update(tab.id, { 
      url: 'https://www.facebook.com/marketplace' 
    });
  }
});
