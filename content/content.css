/* Main content styles for Marketplace Comparator */

/* Ensure our elements don't interfere with Facebook's styles */
.marketplace-toggle-btn,
.marketplace-side-panel,
.panel-toggle-btn {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  box-sizing: border-box;
}

/* Reset for child elements */
.marketplace-side-panel *,
.marketplace-toggle-btn * {
  box-sizing: border-box;
}

/* Ensure high z-index for all our components */
.marketplace-toggle-btn {
  z-index: 999999 !important;
}

.marketplace-side-panel {
  z-index: 999998 !important;
}

.panel-toggle-btn {
  z-index: 999997 !important;
}

/* Smooth transitions for better UX */
.marketplace-listing-container {
  transition: all 0.2s ease;
}

.marketplace-listing-container.selected {
  transform: scale(1.02);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .marketplace-side-panel {
    width: 100vw !important;
    right: -100vw !important;
  }
  
  .marketplace-side-panel.open {
    right: 0 !important;
  }
  
  .panel-toggle-btn.panel-open {
    right: 10px !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .marketplace-side-panel {
    background: #1a1a1a !important;
    border-left-color: #333 !important;
    color: #fff !important;
  }
  
  .panel-header {
    background: #2d2d2d !important;
    border-bottom-color: #333 !important;
  }
  
  .panel-header h3 {
    color: #fff !important;
  }
  
  .control-btn {
    background: #333 !important;
    border-color: #555 !important;
    color: #fff !important;
  }
  
  .control-btn:hover {
    background: #444 !important;
  }
  
  .item-card {
    background: #2d2d2d !important;
    border-color: #333 !important;
  }
  
  .item-title {
    color: #fff !important;
  }
  
  .item-location {
    color: #ccc !important;
  }
  
  .item-date {
    color: #999 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .marketplace-toggle-btn {
    border-width: 3px !important;
  }
  
  .marketplace-listing-container.selected {
    outline-width: 3px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .marketplace-toggle-btn,
  .marketplace-side-panel,
  .marketplace-listing-container,
  .panel-toggle-btn {
    transition: none !important;
  }
  
  .marketplace-listing-container.selected {
    transform: none !important;
  }
  
  .marketplace-toggle-btn:hover {
    transform: none !important;
  }
}
