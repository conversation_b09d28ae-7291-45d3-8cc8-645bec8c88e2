/**
 * Main Content Script - Initializes and coordinates all components
 */
class MarketplaceComparator {
  constructor() {
    this.storageManager = null;
    this.dataExtractor = null;
    this.toggleButton = null;
    this.sidePanel = null;
    this.csvExporter = null;
    this.initialized = false;
    
    this.init();
  }

  /**
   * Initialize the extension
   */
  async init() {
    // Wait for page to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initComponents());
    } else {
      this.initComponents();
    }
  }

  /**
   * Initialize all components
   */
  initComponents() {
    try {
      // Check if we're on a Facebook Marketplace page
      if (!this.isMarketplacePage()) {
        return;
      }

      // Initialize core components
      this.storageManager = new StorageManager();
      this.dataExtractor = new DataExtractor();
      this.csvExporter = new CSVExporter();
      
      // Initialize UI components
      this.toggleButton = new ToggleButton(this.storageManager, this.dataExtractor);
      this.sidePanel = new SidePanel(this.storageManager);
      
      this.initialized = true;
      console.log('Marketplace Comparator initialized');

      // Setup message handling
      this.setupMessageHandling();

      // Handle page navigation (Facebook is a SPA)
      this.setupNavigationHandling();
      
    } catch (error) {
      console.error('Error initializing Marketplace Comparator:', error);
    }
  }

  /**
   * Check if current page is Facebook Marketplace
   */
  isMarketplacePage() {
    return window.location.href.includes('facebook.com/marketplace');
  }

  /**
   * Setup handling for page navigation in Facebook's SPA
   */
  setupNavigationHandling() {
    let currentUrl = window.location.href;
    
    // Monitor URL changes
    const observer = new MutationObserver(() => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        this.handlePageChange();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Also listen for popstate events
    window.addEventListener('popstate', () => {
      setTimeout(() => this.handlePageChange(), 100);
    });
  }

  /**
   * Handle page changes within Facebook
   */
  handlePageChange() {
    if (!this.isMarketplacePage()) {
      this.cleanup();
      return;
    }

    // Reinitialize components if needed
    if (this.initialized) {
      setTimeout(() => {
        if (this.toggleButton) {
          this.toggleButton.addToggleButtons();
        }
      }, 1000); // Wait for page content to load
    }
  }

  /**
   * Cleanup when leaving marketplace
   */
  cleanup() {
    // Remove any UI elements that shouldn't persist
    const panel = document.getElementById('marketplace-side-panel');
    const toggleBtn = document.getElementById('marketplace-panel-toggle');
    
    if (panel) panel.style.display = 'none';
    if (toggleBtn) toggleBtn.style.display = 'none';
  }

  /**
   * Get current status
   */
  async getStatus() {
    if (!this.storageManager) return null;

    const itemCount = await this.storageManager.getItemCount();
    return {
      initialized: this.initialized,
      itemCount: itemCount,
      isMarketplacePage: this.isMarketplacePage(),
      currentUrl: window.location.href
    };
  }

  /**
   * Handle messages from popup or background script
   */
  setupMessageHandling() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.action) {
        case 'togglePanel':
          if (this.sidePanel) {
            this.sidePanel.togglePanel();
          }
          sendResponse({ success: true });
          break;

        case 'exportCSV':
          if (request.items && this.csvExporter) {
            const event = new CustomEvent('marketplaceExportRequest', {
              detail: { items: request.items, format: 'csv' }
            });
            document.dispatchEvent(event);
          }
          sendResponse({ success: true });
          break;

        case 'getStatus':
          this.getStatus().then(status => {
            sendResponse({ success: true, data: status });
          });
          return true; // Async response

        case 'storageChanged':
          // Refresh UI when storage changes from other tabs
          if (this.toggleButton) {
            this.toggleButton.updateButtonStates();
          }
          if (this.sidePanel) {
            this.sidePanel.loadItems();
          }
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    });
  }
}

// Initialize when script loads
let marketplaceComparator;

// Wait a bit for all dependencies to load
setTimeout(() => {
  marketplaceComparator = new MarketplaceComparator();
}, 500);

// Make available globally for debugging
window.marketplaceComparator = marketplaceComparator;
