/**
 * Side Panel Component - Displays selected items in a collapsible side panel
 */
class SidePanel {
  constructor(storageManager) {
    this.storageManager = storageManager;
    this.panel = null;
    this.isOpen = false;
    this.items = [];
    this.init();
  }

  init() {
    this.createPanel();
    this.loadItems();
    this.setupEventListeners();
  }

  /**
   * Create the side panel HTML structure
   */
  createPanel() {
    // Remove existing panel if any
    const existing = document.getElementById('marketplace-side-panel');
    if (existing) existing.remove();

    // Create panel container
    this.panel = document.createElement('div');
    this.panel.id = 'marketplace-side-panel';
    this.panel.className = 'marketplace-side-panel';
    
    this.panel.innerHTML = `
      <div class="panel-header">
        <h3>Selected Items (<span id="item-count">0</span>)</h3>
        <div class="panel-controls">
          <button id="export-btn" class="control-btn" title="Export to CSV">📊</button>
          <button id="clear-all-btn" class="control-btn" title="Clear all">🗑️</button>
          <button id="toggle-panel-btn" class="control-btn" title="Toggle panel">◀</button>
        </div>
      </div>
      <div class="panel-content">
        <div id="items-container" class="items-container">
          <div class="empty-state">
            <p>No items selected</p>
            <p class="hint">Click the + button on listings to add them here</p>
          </div>
        </div>
      </div>
    `;

    // Add styles
    this.addStyles();

    // Add to page
    document.body.appendChild(this.panel);
  }

  /**
   * Add CSS styles for the side panel
   */
  addStyles() {
    if (document.getElementById('marketplace-side-panel-styles')) return;

    const style = document.createElement('style');
    style.id = 'marketplace-side-panel-styles';
    style.textContent = `
      .marketplace-side-panel {
        position: fixed;
        top: 0;
        right: -400px;
        width: 400px;
        height: 100vh;
        background: white;
        border-left: 1px solid #ddd;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        transition: right 0.3s ease;
        display: flex;
        flex-direction: column;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .marketplace-side-panel.open {
        right: 0;
      }

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .panel-header h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }

      .panel-controls {
        display: flex;
        gap: 8px;
      }

      .control-btn {
        background: none;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px 8px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.2s;
      }

      .control-btn:hover {
        background: #e9ecef;
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
      }

      .items-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .empty-state {
        text-align: center;
        color: #666;
        margin-top: 50px;
      }

      .empty-state .hint {
        font-size: 14px;
        color: #999;
      }

      .item-card {
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 12px;
        background: white;
        transition: box-shadow 0.2s;
      }

      .item-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .item-header {
        display: flex;
        gap: 12px;
        margin-bottom: 8px;
      }

      .item-thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        object-fit: cover;
        background: #f0f0f0;
      }

      .item-info {
        flex: 1;
        min-width: 0;
      }

      .item-title {
        font-weight: 600;
        font-size: 14px;
        color: #333;
        margin: 0 0 4px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .item-price {
        font-size: 16px;
        font-weight: 700;
        color: #42b883;
        margin: 0 0 4px 0;
      }

      .item-location {
        font-size: 12px;
        color: #666;
        margin: 0;
      }

      .item-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
      }

      .item-date {
        font-size: 11px;
        color: #999;
      }

      .remove-btn {
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        transition: background 0.2s;
      }

      .remove-btn:hover {
        background: #c82333;
      }

      .view-btn {
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        text-decoration: none;
        transition: background 0.2s;
      }

      .view-btn:hover {
        background: #0056b3;
      }

      .panel-toggle-btn {
        position: fixed;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        background: #42b883;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        cursor: pointer;
        font-size: 18px;
        z-index: 9999;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
      }

      .panel-toggle-btn:hover {
        background: #369870;
        transform: translateY(-50%) scale(1.1);
      }

      .panel-toggle-btn.panel-open {
        right: 410px;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Toggle panel button
    const toggleBtn = this.panel.querySelector('#toggle-panel-btn');
    toggleBtn.addEventListener('click', () => this.togglePanel());

    // Export button
    const exportBtn = this.panel.querySelector('#export-btn');
    exportBtn.addEventListener('click', () => this.exportItems());

    // Clear all button
    const clearBtn = this.panel.querySelector('#clear-all-btn');
    clearBtn.addEventListener('click', () => this.clearAllItems());

    // Listen for selection changes
    document.addEventListener('marketplaceSelectionChange', () => {
      this.loadItems();
    });

    // Create floating toggle button
    this.createFloatingToggle();
  }

  /**
   * Create floating toggle button
   */
  createFloatingToggle() {
    const existing = document.getElementById('marketplace-panel-toggle');
    if (existing) existing.remove();

    const toggleBtn = document.createElement('button');
    toggleBtn.id = 'marketplace-panel-toggle';
    toggleBtn.className = 'panel-toggle-btn';
    toggleBtn.innerHTML = '📋';
    toggleBtn.title = 'Toggle comparison panel';

    toggleBtn.addEventListener('click', () => this.togglePanel());

    document.body.appendChild(toggleBtn);
  }

  /**
   * Toggle panel open/closed
   */
  togglePanel() {
    this.isOpen = !this.isOpen;
    const toggleBtn = document.getElementById('marketplace-panel-toggle');
    
    if (this.isOpen) {
      this.panel.classList.add('open');
      toggleBtn.classList.add('panel-open');
      this.loadItems(); // Refresh items when opening
    } else {
      this.panel.classList.remove('open');
      toggleBtn.classList.remove('panel-open');
    }
  }

  /**
   * Load and display items
   */
  async loadItems() {
    this.items = await this.storageManager.getSelectedItems();
    this.renderItems();
    this.updateItemCount();
  }

  /**
   * Render items in the panel
   */
  renderItems() {
    const container = this.panel.querySelector('#items-container');
    
    if (this.items.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <p>No items selected</p>
          <p class="hint">Click the + button on listings to add them here</p>
        </div>
      `;
      return;
    }

    container.innerHTML = this.items.map(item => this.renderItemCard(item)).join('');

    // Add event listeners to action buttons
    container.querySelectorAll('.remove-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const url = e.target.dataset.url;
        this.removeItem(url);
      });
    });
  }

  /**
   * Render individual item card
   */
  renderItemCard(item) {
    const thumbnail = item.thumbnail || item.images?.[0] || '';
    const title = item.title || 'Untitled';
    const price = item.price || 'Price not available';
    const location = item.location || 'Location not available';
    const dateSelected = item.dateSelected ? 
      new Date(item.dateSelected).toLocaleDateString() : 'Unknown';

    return `
      <div class="item-card">
        <div class="item-header">
          ${thumbnail ? `<img src="${thumbnail}" alt="${title}" class="item-thumbnail">` : 
            '<div class="item-thumbnail" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999;">📷</div>'}
          <div class="item-info">
            <h4 class="item-title" title="${title}">${title}</h4>
            <p class="item-price">${price}</p>
            <p class="item-location">${location}</p>
          </div>
        </div>
        <div class="item-actions">
          <span class="item-date">Added: ${dateSelected}</span>
          <div>
            <a href="${item.url}" target="_blank" class="view-btn">View</a>
            <button class="remove-btn" data-url="${item.url}">Remove</button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Update item count display
   */
  updateItemCount() {
    const countElement = this.panel.querySelector('#item-count');
    countElement.textContent = this.items.length;

    // Update floating button badge
    const toggleBtn = document.getElementById('marketplace-panel-toggle');
    if (this.items.length > 0) {
      toggleBtn.innerHTML = `📋 ${this.items.length}`;
    } else {
      toggleBtn.innerHTML = '📋';
    }
  }

  /**
   * Remove item from selection
   */
  async removeItem(url) {
    await this.storageManager.removeItem(url);
    await this.loadItems();
    
    // Notify other components
    const event = new CustomEvent('marketplaceSelectionChange', {
      detail: { action: 'removed', timestamp: Date.now() }
    });
    document.dispatchEvent(event);
  }

  /**
   * Clear all items
   */
  async clearAllItems() {
    if (this.items.length === 0) return;
    
    if (confirm(`Remove all ${this.items.length} selected items?`)) {
      await this.storageManager.clearAllItems();
      await this.loadItems();
      
      // Notify other components
      const event = new CustomEvent('marketplaceSelectionChange', {
        detail: { action: 'cleared', timestamp: Date.now() }
      });
      document.dispatchEvent(event);
    }
  }

  /**
   * Export items to CSV
   */
  exportItems() {
    if (this.items.length === 0) {
      alert('No items to export');
      return;
    }

    // Trigger CSV export
    const event = new CustomEvent('marketplaceExportRequest', {
      detail: { items: this.items, format: 'csv' }
    });
    document.dispatchEvent(event);
  }
}

// Make available globally
window.SidePanel = SidePanel;
