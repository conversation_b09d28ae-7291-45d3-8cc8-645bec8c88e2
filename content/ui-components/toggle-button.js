/**
 * Toggle Button Component - Creates and manages selection toggle buttons
 */
class ToggleButton {
  constructor(storageManager, dataExtractor) {
    this.storageManager = storageManager;
    this.dataExtractor = dataExtractor;
    this.buttons = new Map(); // Track buttons by element
    this.init();
  }

  init() {
    this.createStyles();
    this.observePageChanges();
    this.addToggleButtons();
  }

  /**
   * Create CSS styles for toggle buttons
   */
  createStyles() {
    if (document.getElementById('marketplace-comparator-styles')) return;

    const style = document.createElement('style');
    style.id = 'marketplace-comparator-styles';
    style.textContent = `
      .marketplace-toggle-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid #fff;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        z-index: 1000;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }

      .marketplace-toggle-btn:hover {
        background: rgba(0, 0, 0, 0.9);
        transform: scale(1.1);
      }

      .marketplace-toggle-btn.selected {
        background: #42b883;
        border-color: #42b883;
      }

      .marketplace-toggle-btn.selected:hover {
        background: #369870;
      }

      .marketplace-listing-container {
        position: relative;
      }

      .marketplace-listing-container.selected {
        outline: 2px solid #42b883;
        outline-offset: 2px;
        border-radius: 8px;
      }

      .marketplace-toggle-btn .icon {
        font-size: 14px;
        line-height: 1;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Observe page changes for dynamic content
   */
  observePageChanges() {
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        setTimeout(() => this.addToggleButtons(), 500);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Add toggle buttons to all listings on the page
   */
  async addToggleButtons() {
    if (this.dataExtractor.isSearchResultsPage()) {
      await this.addButtonsToSearchResults();
    } else if (this.dataExtractor.isListingPage()) {
      await this.addButtonToListingPage();
    }
  }

  /**
   * Add buttons to search results page
   */
  async addButtonsToSearchResults() {
    // Look for marketplace listing containers
    const selectors = [
      '[data-pagelet="MarketplaceSearchResults"] [role="main"] > div > div',
      '[data-pagelet="MarketplaceSearchResults"] a[role="link"]',
      'div[data-pagelet="MarketplaceSearchResults"] > div > div > div',
      'a[href*="/marketplace/item/"]'
    ];

    let listingElements = [];
    
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        listingElements = Array.from(elements);
        break;
      }
    }

    // Filter to get actual listing containers
    listingElements = listingElements.filter(el => {
      const hasImage = el.querySelector('img');
      const hasText = el.textContent.trim().length > 10;
      const hasLink = el.querySelector('a[href*="/marketplace/item/"]') || el.href?.includes('/marketplace/item/');
      return hasImage && hasText && hasLink;
    });

    for (const element of listingElements) {
      await this.addButtonToElement(element);
    }
  }

  /**
   * Add button to individual listing page
   */
  async addButtonToListingPage() {
    const container = document.querySelector('[data-pagelet="MarketplaceProductDetailsPageLayout"]') ||
                     document.querySelector('main') ||
                     document.body;
    
    if (container && !this.buttons.has(container)) {
      await this.addButtonToElement(container, true);
    }
  }

  /**
   * Add toggle button to a specific element
   */
  async addButtonToElement(element, isListingPage = false) {
    if (this.buttons.has(element)) return;

    // Make container relative for absolute positioning
    if (!element.classList.contains('marketplace-listing-container')) {
      element.classList.add('marketplace-listing-container');
    }

    // Get URL for this listing
    let url = '';
    if (isListingPage) {
      url = window.location.href;
    } else {
      const linkElement = element.querySelector('a[href*="/marketplace/item/"]') || 
                         (element.href?.includes('/marketplace/item/') ? element : null);
      if (linkElement) {
        const href = linkElement.href || linkElement.getAttribute('href');
        url = href ? new URL(href, 'https://www.facebook.com').href : '';
      }
    }

    if (!url) return;

    // Check if already selected
    const isSelected = await this.storageManager.isItemSelected(url);

    // Create toggle button
    const button = document.createElement('div');
    button.className = `marketplace-toggle-btn ${isSelected ? 'selected' : ''}`;
    button.innerHTML = `<span class="icon">${isSelected ? '✓' : '+'}</span>`;
    button.title = isSelected ? 'Remove from comparison' : 'Add to comparison';

    // Position button
    if (isListingPage) {
      button.style.position = 'fixed';
      button.style.top = '20px';
      button.style.right = '20px';
      button.style.zIndex = '10000';
    }

    // Add click handler
    button.addEventListener('click', async (e) => {
      e.preventDefault();
      e.stopPropagation();
      await this.handleToggleClick(button, element, url, isListingPage);
    });

    // Add button to element
    element.appendChild(button);
    this.buttons.set(element, { button, url, isListingPage });

    // Update container selection state
    if (isSelected) {
      element.classList.add('selected');
    }
  }

  /**
   * Handle toggle button click
   */
  async handleToggleClick(button, element, url, isListingPage) {
    const isCurrentlySelected = button.classList.contains('selected');

    if (isCurrentlySelected) {
      // Remove from selection
      const success = await this.storageManager.removeItem(url);
      if (success) {
        button.classList.remove('selected');
        button.innerHTML = '<span class="icon">+</span>';
        button.title = 'Add to comparison';
        element.classList.remove('selected');
        this.notifySelectionChange('removed');
      }
    } else {
      // Add to selection
      let data;
      if (isListingPage) {
        data = this.dataExtractor.extractFromListingPage();
      } else {
        data = this.dataExtractor.extractFromSearchResult(element);
      }

      if (data) {
        const success = await this.storageManager.addItem(data);
        if (success) {
          button.classList.add('selected');
          button.innerHTML = '<span class="icon">✓</span>';
          button.title = 'Remove from comparison';
          element.classList.add('selected');
          this.notifySelectionChange('added', data);
        }
      }
    }
  }

  /**
   * Notify other components of selection changes
   */
  notifySelectionChange(action, data = null) {
    const event = new CustomEvent('marketplaceSelectionChange', {
      detail: { action, data, timestamp: Date.now() }
    });
    document.dispatchEvent(event);
  }

  /**
   * Update button states based on current storage
   */
  async updateButtonStates() {
    for (const [element, buttonData] of this.buttons) {
      const { button, url } = buttonData;
      const isSelected = await this.storageManager.isItemSelected(url);
      
      if (isSelected) {
        button.classList.add('selected');
        button.innerHTML = '<span class="icon">✓</span>';
        button.title = 'Remove from comparison';
        element.classList.add('selected');
      } else {
        button.classList.remove('selected');
        button.innerHTML = '<span class="icon">+</span>';
        button.title = 'Add to comparison';
        element.classList.remove('selected');
      }
    }
  }
}

// Make available globally
window.ToggleButton = ToggleButton;
