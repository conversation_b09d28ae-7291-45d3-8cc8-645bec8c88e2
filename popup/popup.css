/* Popup styles for Marketplace Comparator */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #fff;
}

.popup-container {
  width: 350px;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

/* Header */
.popup-header {
  padding: 16px;
  background: linear-gradient(135deg, #42b883, #369870);
  color: white;
  text-align: center;
}

.popup-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #fff;
  opacity: 0.8;
}

.status-dot.active {
  background: #4ade80;
  opacity: 1;
}

.status-dot.inactive {
  background: #f87171;
  opacity: 1;
}

/* Content */
.popup-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #42b883;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Actions Section */
.actions-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.primary {
  background: #42b883;
  color: white;
}

.action-btn.primary:hover {
  background: #369870;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.action-btn.danger {
  background: #dc3545;
  color: white;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 16px;
}

/* Info Section */
.info-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #42b883;
}

.info-section h3 {
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
}

.info-section ol {
  padding-left: 16px;
}

.info-section li {
  margin-bottom: 4px;
  font-size: 13px;
  color: #666;
}

/* Settings Section */
.settings-section {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
}

.settings-section h3 {
  font-size: 14px;
  margin-bottom: 12px;
  color: #333;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #42b883;
}

/* Footer */
.popup-footer {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 12px;
}

.footer-links a {
  font-size: 12px;
  color: #666;
  text-decoration: none;
}

.footer-links a:hover {
  color: #42b883;
}

.version {
  font-size: 11px;
  color: #999;
}

/* Loading state */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Success/Error messages */
.message {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 12px;
  text-align: center;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  body {
    background: #1a1a1a;
    color: #fff;
  }
  
  .popup-container {
    background: #1a1a1a;
  }
  
  .stat-item {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .info-section,
  .popup-footer {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .settings-section {
    border-color: #404040;
  }
  
  .footer-links a {
    color: #ccc;
  }
  
  .footer-links a:hover {
    color: #42b883;
  }
}
