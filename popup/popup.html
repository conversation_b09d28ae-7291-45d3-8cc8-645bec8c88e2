<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Marketplace Deal Comparator</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <header class="popup-header">
      <h1>Marketplace Comparator</h1>
      <div class="status-indicator" id="status-indicator">
        <span class="status-dot"></span>
        <span class="status-text">Loading...</span>
      </div>
    </header>

    <main class="popup-content">
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-number" id="item-count">0</span>
          <span class="stat-label">Items Selected</span>
        </div>
        <div class="stat-item">
          <span class="stat-number" id="storage-usage">0%</span>
          <span class="stat-label">Storage Used</span>
        </div>
      </div>

      <div class="actions-section">
        <button id="toggle-panel-btn" class="action-btn primary">
          <span class="btn-icon">📋</span>
          <span class="btn-text">Toggle Panel</span>
        </button>
        
        <button id="export-csv-btn" class="action-btn secondary">
          <span class="btn-icon">📊</span>
          <span class="btn-text">Export CSV</span>
        </button>
        
        <button id="clear-all-btn" class="action-btn danger">
          <span class="btn-icon">🗑️</span>
          <span class="btn-text">Clear All</span>
        </button>
      </div>

      <div class="info-section">
        <h3>How to Use:</h3>
        <ol>
          <li>Browse Facebook Marketplace</li>
          <li>Click the <strong>+</strong> button on listings to select them</li>
          <li>View selected items in the side panel</li>
          <li>Export your comparison to CSV</li>
        </ol>
      </div>

      <div class="settings-section">
        <h3>Settings:</h3>
        <label class="setting-item">
          <input type="checkbox" id="auto-open-panel">
          <span>Auto-open panel when items selected</span>
        </label>
        <label class="setting-item">
          <input type="checkbox" id="include-images">
          <span>Include image URLs in export</span>
        </label>
      </div>
    </main>

    <footer class="popup-footer">
      <div class="footer-links">
        <a href="#" id="help-link">Help</a>
        <a href="#" id="feedback-link">Feedback</a>
      </div>
      <div class="version">v1.0.0</div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>
