/**
 * Popup Script - Handles popup interface interactions
 */

class PopupManager {
  constructor() {
    this.currentTab = null;
    this.init();
  }

  async init() {
    await this.getCurrentTab();
    this.setupEventListeners();
    this.loadStatus();
    this.loadSettings();
  }

  /**
   * Get current active tab
   */
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Toggle panel button
    document.getElementById('toggle-panel-btn').addEventListener('click', () => {
      this.togglePanel();
    });

    // Export CSV button
    document.getElementById('export-csv-btn').addEventListener('click', () => {
      this.exportCSV();
    });

    // Clear all button
    document.getElementById('clear-all-btn').addEventListener('click', () => {
      this.clearAll();
    });

    // Settings checkboxes
    document.getElementById('auto-open-panel').addEventListener('change', (e) => {
      this.saveSetting('autoOpenPanel', e.target.checked);
    });

    document.getElementById('include-images').addEventListener('change', (e) => {
      this.saveSetting('includeImages', e.target.checked);
    });

    // Help and feedback links
    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelp();
    });

    document.getElementById('feedback-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.openFeedback();
    });
  }

  /**
   * Load and display current status
   */
  async loadStatus() {
    try {
      // Check if we're on a marketplace page
      const isMarketplacePage = this.currentTab?.url?.includes('facebook.com/marketplace');
      
      // Update status indicator
      const statusIndicator = document.getElementById('status-indicator');
      const statusDot = statusIndicator.querySelector('.status-dot');
      const statusText = statusIndicator.querySelector('.status-text');
      
      if (isMarketplacePage) {
        statusDot.className = 'status-dot active';
        statusText.textContent = 'Active on Marketplace';
      } else {
        statusDot.className = 'status-dot inactive';
        statusText.textContent = 'Not on Marketplace';
      }

      // Get storage data
      const result = await chrome.storage.local.get(['marketplace_selected_items']);
      const items = result.marketplace_selected_items || [];
      
      // Update item count
      document.getElementById('item-count').textContent = items.length;
      
      // Get storage usage
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES || 5242880; // 5MB default
      const percentage = Math.round((usage / quota) * 100);
      document.getElementById('storage-usage').textContent = `${percentage}%`;
      
      // Enable/disable buttons based on state
      const exportBtn = document.getElementById('export-csv-btn');
      const clearBtn = document.getElementById('clear-all-btn');
      const toggleBtn = document.getElementById('toggle-panel-btn');
      
      exportBtn.disabled = items.length === 0;
      clearBtn.disabled = items.length === 0;
      toggleBtn.disabled = !isMarketplacePage;
      
    } catch (error) {
      console.error('Error loading status:', error);
      this.showMessage('Error loading status', 'error');
    }
  }

  /**
   * Load saved settings
   */
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['autoOpenPanel', 'includeImages']);
      
      document.getElementById('auto-open-panel').checked = result.autoOpenPanel || false;
      document.getElementById('include-images').checked = result.includeImages !== false; // Default true
      
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  /**
   * Save a setting
   */
  async saveSetting(key, value) {
    try {
      await chrome.storage.sync.set({ [key]: value });
    } catch (error) {
      console.error('Error saving setting:', error);
    }
  }

  /**
   * Toggle side panel
   */
  async togglePanel() {
    if (!this.currentTab?.url?.includes('facebook.com/marketplace')) {
      this.showMessage('Please navigate to Facebook Marketplace first', 'error');
      return;
    }

    try {
      await chrome.tabs.sendMessage(this.currentTab.id, { action: 'togglePanel' });
      window.close(); // Close popup after action
    } catch (error) {
      console.error('Error toggling panel:', error);
      this.showMessage('Error communicating with page', 'error');
    }
  }

  /**
   * Export to CSV
   */
  async exportCSV() {
    try {
      const result = await chrome.storage.local.get(['marketplace_selected_items']);
      const items = result.marketplace_selected_items || [];
      
      if (items.length === 0) {
        this.showMessage('No items to export', 'error');
        return;
      }

      // If on marketplace page, send message to content script
      if (this.currentTab?.url?.includes('facebook.com/marketplace')) {
        await chrome.tabs.sendMessage(this.currentTab.id, { 
          action: 'exportCSV',
          items: items 
        });
        this.showMessage('Export started', 'success');
      } else {
        // Fallback: download directly from popup
        this.downloadCSVFromPopup(items);
      }
      
    } catch (error) {
      console.error('Error exporting CSV:', error);
      this.showMessage('Error exporting data', 'error');
    }
  }

  /**
   * Download CSV directly from popup (fallback)
   */
  downloadCSVFromPopup(items) {
    // Simple CSV generation for popup fallback
    const headers = ['Title', 'Price', 'Location', 'URL', 'Date_Selected'];
    const rows = items.map(item => [
      item.title || '',
      item.price || '',
      item.location || '',
      item.url || '',
      item.dateSelected || ''
    ].map(field => `"${field.replace(/"/g, '""')}"`).join(','));
    
    const csv = [headers.join(','), ...rows].join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `marketplace-comparison-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    
    URL.revokeObjectURL(url);
    this.showMessage('CSV downloaded', 'success');
  }

  /**
   * Clear all selected items
   */
  async clearAll() {
    try {
      const result = await chrome.storage.local.get(['marketplace_selected_items']);
      const items = result.marketplace_selected_items || [];
      
      if (items.length === 0) {
        this.showMessage('No items to clear', 'error');
        return;
      }

      if (confirm(`Clear all ${items.length} selected items?`)) {
        await chrome.storage.local.set({ marketplace_selected_items: [] });
        this.showMessage('All items cleared', 'success');
        this.loadStatus(); // Refresh status
      }
      
    } catch (error) {
      console.error('Error clearing items:', error);
      this.showMessage('Error clearing items', 'error');
    }
  }

  /**
   * Show message to user
   */
  showMessage(text, type = 'success') {
    // Remove existing messages
    const existing = document.querySelector('.message');
    if (existing) existing.remove();
    
    const message = document.createElement('div');
    message.className = `message ${type}`;
    message.textContent = text;
    
    const content = document.querySelector('.popup-content');
    content.insertBefore(message, content.firstChild);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (message.parentNode) {
        message.parentNode.removeChild(message);
      }
    }, 3000);
  }

  /**
   * Open help page
   */
  openHelp() {
    chrome.tabs.create({ 
      url: 'https://github.com/your-repo/marketplace-comparator#readme' 
    });
  }

  /**
   * Open feedback page
   */
  openFeedback() {
    chrome.tabs.create({ 
      url: 'https://github.com/your-repo/marketplace-comparator/issues' 
    });
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
