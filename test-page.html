<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Facebook Marketplace Structure</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .listing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        .listing-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .listing-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .listing-content {
            padding: 12px;
        }
        .listing-price {
            font-size: 18px;
            font-weight: bold;
            color: #1877f2;
            margin-bottom: 4px;
        }
        .listing-price span:nth-child(2) {
            text-decoration: line-through;
            color: #666;
            font-size: 14px;
            margin-left: 8px;
        }
        .listing-title {
            font-size: 14px;
            margin-bottom: 4px;
            color: #333;
        }
        .listing-location {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Facebook Marketplace Extension</h1>
        <p>This page simulates the Facebook Marketplace structure to test the extension.</p>
        
        <div class="listing-grid">
            <!-- Simulate the actual Facebook structure from samplelistings.html -->
            <div class="x9f619 x78zum5 x1r8uery xdt5ytf x1iyjqo2 xs83m0k x135b78x x11lfxj5 x1iorvi4 xjkvuk6 xnpuxes x1cjf5ee x17dddeq">
                <div class="listing-item">
                    <a href="/marketplace/item/456698653816625/" class="x1i10hfl">
                        <img src="https://via.placeholder.com/300x200?text=Dell+Laptop" alt="Dell 3420 model" class="listing-image">
                        <div class="listing-content">
                            <div class="listing-price">
                                <span class="x193iq5w xeuugli" dir="auto">CA$500</span>
                                <span class="x193iq5w xeuugli" dir="auto">CA$530</span>
                            </div>
                            <div class="listing-title">
                                <span dir="auto">New Dell 3420 model</span>
                            </div>
                            <div class="listing-location">
                                <span class="x1lliihq x6ikm8r x10wlt62 x1n2onr6 xlyipyv xuxw1ft">Moncton, NB</span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <div class="x9f619 x78zum5 x1r8uery xdt5ytf x1iyjqo2 xs83m0k x135b78x x11lfxj5 x1iorvi4 xjkvuk6 xnpuxes x1cjf5ee x17dddeq">
                <div class="listing-item">
                    <a href="/marketplace/item/1969780173832334/" class="x1i10hfl">
                        <img src="https://via.placeholder.com/300x200?text=HP+Laptop" alt="Used HP Laptops" class="listing-image">
                        <div class="listing-content">
                            <div class="listing-price">
                                <span class="x193iq5w xeuugli" dir="auto">CA$395</span>
                            </div>
                            <div class="listing-title">
                                <span dir="auto">Used HP Laptops</span>
                            </div>
                            <div class="listing-location">
                                <span class="x1lliihq x6ikm8r x10wlt62 x1n2onr6 xlyipyv xuxw1ft">Moncton, NB</span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <div class="x9f619 x78zum5 x1r8uery xdt5ytf x1iyjqo2 xs83m0k x135b78x x11lfxj5 x1iorvi4 xjkvuk6 xnpuxes x1cjf5ee x17dddeq">
                <div class="listing-item">
                    <a href="/marketplace/item/470437902225005/" class="x1i10hfl">
                        <img src="https://via.placeholder.com/300x200?text=Dell+Laptop+2" alt="Dell laptop" class="listing-image">
                        <div class="listing-content">
                            <div class="listing-price">
                                <span class="x193iq5w xeuugli" dir="auto">CA$120</span>
                            </div>
                            <div class="listing-title">
                                <span dir="auto">Dell laptop</span>
                            </div>
                            <div class="listing-location">
                                <span class="x1lliihq x6ikm8r x10wlt62 x1n2onr6 xlyipyv xuxw1ft">Moncton, NB</span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulate being on Facebook Marketplace
        console.log('Test page loaded. URL:', window.location.href);
        
        // Add marketplace to URL for testing
        if (!window.location.href.includes('marketplace')) {
            console.log('Adding marketplace to URL for testing');
            history.replaceState({}, '', window.location.href + '?marketplace=test');
        }
        
        // Log when extension should initialize
        setTimeout(() => {
            console.log('Extension should have initialized by now...');
            const buttons = document.querySelectorAll('.marketplace-toggle-btn');
            console.log('Found toggle buttons:', buttons.length);

            // Test data extraction manually
            const listings = document.querySelectorAll('.x9f619.x78zum5.x1r8uery.xdt5ytf.x1iyjqo2.xs83m0k.x135b78x.x11lfxj5.x1iorvi4.xjkvuk6.xnpuxes.x1cjf5ee.x17dddeq');
            console.log('Found listing containers:', listings.length);

            listings.forEach((listing, index) => {
                console.log(`Listing ${index + 1} text content:`, listing.textContent.trim());
                const titleSpans = listing.querySelectorAll('span[dir="auto"]');
                const locationSpans = listing.querySelectorAll('span.x1lliihq.x6ikm8r.x10wlt62.x1n2onr6.xlyipyv.xuxw1ft');
                console.log(`  Title spans:`, Array.from(titleSpans).map(s => s.textContent.trim()));
                console.log(`  Location spans:`, Array.from(locationSpans).map(s => s.textContent.trim()));
            });
        }, 3000);
    </script>
</body>
</html>
