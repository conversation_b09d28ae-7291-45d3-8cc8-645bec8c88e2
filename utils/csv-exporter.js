/**
 * CSV Exporter - Handles exporting selected items to CSV format
 */
class CSVExporter {
  constructor() {
    this.setupEventListeners();
  }

  /**
   * Setup event listeners for export requests
   */
  setupEventListeners() {
    document.addEventListener('marketplaceExportRequest', (event) => {
      const { items, format } = event.detail;
      if (format === 'csv') {
        this.exportToCSV(items);
      }
    });
  }

  /**
   * Export items to CSV format
   */
  exportToCSV(items) {
    if (!items || items.length === 0) {
      alert('No items to export');
      return;
    }

    try {
      const csv = this.generateCSV(items);
      this.downloadCSV(csv, this.generateFilename());
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      alert('Error exporting data. Please try again.');
    }
  }

  /**
   * Generate CSV content from items
   */
  generateCSV(items) {
    // Define CSV headers
    const headers = [
      'Title',
      'Current_Price',
      'Original_Price',
      'Current_Price_Numeric',
      'Original_Price_Numeric',
      'Is_Markdown',
      'Discount_Amount',
      'Discount_Percent',
      'Location',
      'Condition',
      'Model',
      'Specifications',
      'Description',
      'Images',
      'URL',
      'Date_Posted',
      'Date_Selected',
      'Category',
      'Type',
      'Extracted_At'
    ];

    // Generate CSV rows
    const rows = items.map(item => this.itemToCSVRow(item, headers));

    // Combine headers and rows
    const csvContent = [
      headers.join(','),
      ...rows
    ].join('\n');

    return csvContent;
  }

  /**
   * Convert item to CSV row
   */
  itemToCSVRow(item, headers) {
    const row = headers.map(header => {
      let value = '';

      switch (header) {
        case 'Title':
          value = item.title || '';
          break;
        case 'Current_Price':
          value = item.currentPrice || item.price || '';
          break;
        case 'Original_Price':
          value = item.originalPrice || '';
          break;
        case 'Current_Price_Numeric':
          value = item.priceNumeric || '';
          break;
        case 'Original_Price_Numeric':
          value = item.originalPriceNumeric || '';
          break;
        case 'Is_Markdown':
          value = item.isMarkdown ? 'Yes' : 'No';
          break;
        case 'Discount_Amount':
          value = item.discountAmount || '';
          break;
        case 'Discount_Percent':
          value = item.discountPercent ? `${item.discountPercent}%` : '';
          break;
        case 'Location':
          value = item.location || '';
          break;
        case 'Condition':
          value = this.extractCondition(item);
          break;
        case 'Model':
          value = this.extractModel(item);
          break;
        case 'Specifications':
          value = this.extractSpecifications(item);
          break;
        case 'Description':
          value = item.description || '';
          break;
        case 'Images':
          value = item.images ? item.images.join('; ') : '';
          break;
        case 'URL':
          value = item.url || '';
          break;
        case 'Date_Posted':
          value = item.datePosted || '';
          break;
        case 'Date_Selected':
          value = item.dateSelected || '';
          break;
        case 'Category':
          value = this.extractCategory(item);
          break;
        case 'Type':
          value = item.type || '';
          break;
        case 'Extracted_At':
          value = item.extractedAt || '';
          break;
        default:
          value = '';
      }

      // Escape and quote the value for CSV
      return this.escapeCSVValue(value);
    });

    return row.join(',');
  }

  /**
   * Extract condition from item data
   */
  extractCondition(item) {
    const text = (item.rawText || item.description || item.title || '').toLowerCase();
    const conditions = ['new', 'like new', 'excellent', 'good', 'fair', 'poor', 'used', 'refurbished'];
    
    for (const condition of conditions) {
      if (text.includes(condition)) {
        return condition.charAt(0).toUpperCase() + condition.slice(1);
      }
    }
    
    return '';
  }

  /**
   * Extract brand from item data
   */
  extractBrand(item) {
    const text = (item.title || item.description || '').toLowerCase();
    
    // Common laptop brands
    const brands = [
      'apple', 'macbook', 'imac',
      'dell', 'hp', 'lenovo', 'asus', 'acer', 'msi', 'alienware',
      'microsoft', 'surface', 'samsung', 'lg', 'sony', 'toshiba',
      'razer', 'origin', 'system76', 'framework'
    ];

    for (const brand of brands) {
      if (text.includes(brand)) {
        return brand.charAt(0).toUpperCase() + brand.slice(1);
      }
    }

    return '';
  }

  /**
   * Extract model from item data
   */
  extractModel(item) {
    const text = item.title || item.description || '';
    
    // Look for model patterns (letters/numbers combinations)
    const modelPatterns = [
      /\b[A-Z]\d{3,4}\b/g, // Like X1, T480, etc.
      /\b\d{4}[A-Z]?\b/g,  // Like 2021, 2020M, etc.
      /\bMacBook\s+(Air|Pro)\b/gi,
      /\bSurface\s+(Pro|Laptop|Book)\s*\d*/gi
    ];

    for (const pattern of modelPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        return matches[0];
      }
    }

    return '';
  }

  /**
   * Extract specifications from item data
   */
  extractSpecifications(item) {
    const text = (item.rawText || item.description || item.title || '').toLowerCase();
    const specs = [];

    // RAM
    const ramMatch = text.match(/(\d+)\s*gb\s*(ram|memory)/i);
    if (ramMatch) {
      specs.push(`RAM: ${ramMatch[1]}GB`);
    }

    // Storage
    const storageMatch = text.match(/(\d+)\s*(gb|tb)\s*(ssd|hdd|storage)/i);
    if (storageMatch) {
      specs.push(`Storage: ${storageMatch[1]}${storageMatch[2].toUpperCase()} ${storageMatch[3].toUpperCase()}`);
    }

    // Processor
    const processorMatch = text.match(/(intel|amd|apple|m1|m2|i3|i5|i7|i9|ryzen)/i);
    if (processorMatch) {
      specs.push(`Processor: ${processorMatch[1]}`);
    }

    // Screen size
    const screenMatch = text.match(/(\d+\.?\d*)\s*inch/i);
    if (screenMatch) {
      specs.push(`Screen: ${screenMatch[1]}"`);
    }

    return specs.join('; ');
  }

  /**
   * Extract category from item data
   */
  extractCategory(item) {
    const text = (item.title || item.description || '').toLowerCase();
    
    const categories = {
      'laptop': ['laptop', 'notebook', 'macbook', 'chromebook'],
      'desktop': ['desktop', 'pc', 'computer', 'imac', 'all-in-one'],
      'tablet': ['tablet', 'ipad', 'surface'],
      'phone': ['phone', 'iphone', 'android', 'smartphone'],
      'monitor': ['monitor', 'display', 'screen'],
      'gaming': ['gaming', 'xbox', 'playstation', 'nintendo', 'ps4', 'ps5']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return category.charAt(0).toUpperCase() + category.slice(1);
      }
    }

    return 'Other';
  }

  /**
   * Escape CSV value (handle quotes, commas, newlines)
   */
  escapeCSVValue(value) {
    if (typeof value !== 'string') {
      value = String(value);
    }

    // If value contains comma, quote, or newline, wrap in quotes and escape quotes
    if (value.includes(',') || value.includes('"') || value.includes('\n') || value.includes('\r')) {
      value = '"' + value.replace(/"/g, '""') + '"';
    }

    return value;
  }

  /**
   * Generate filename for CSV export
   */
  generateFilename() {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
    return `marketplace-comparison-${dateStr}-${timeStr}.csv`;
  }

  /**
   * Download CSV file
   */
  downloadCSV(csvContent, filename) {
    // Create blob
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    
    // Create download link
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    // Add to page, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    URL.revokeObjectURL(url);
    
    // Show success message
    this.showExportSuccess(filename);
  }

  /**
   * Show export success message
   */
  showExportSuccess(filename) {
    // Create temporary notification
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #42b883;
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 10001;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    `;
    notification.textContent = `Exported: ${filename}`;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }
}

// Make available globally
window.CSVExporter = CSVExporter;
