/**
 * Data Extractor - Extracts listing data from Facebook Marketplace pages
 */
class DataExtractor {
  constructor() {
    this.selectors = {
      // Updated selectors based on actual Facebook Marketplace structure
      searchResult: 'div.x9f619.x78zum5.x1r8uery.xdt5ytf.x1iyjqo2.xs83m0k.x135b78x.x11lfxj5.x1iorvi4.xjkvuk6.xnpuxes.x1cjf5ee.x17dddeq',
      searchLink: 'a.x1i10hfl[href*="/marketplace/item/"]',
      searchImage: 'img.x15mokao',
      searchTitle: 'span[dir="auto"]',
      searchPrice: 'span.x193iq5w.xeuugli[dir="auto"]',
      searchLocation: 'span.x1lliihq.x6ikm8r.x10wlt62.x1n2onr6.xlyipyv.xuxw1ft',

      // Individual listing page selectors (will be updated when we have sample)
      listingTitle: 'h1',
      listingPrice: 'span[dir="auto"]',
      listingLocation: 'span',
      listingDescription: 'span[dir="auto"]',
      listingSeller: 'strong',
      listingImages: 'img',
      listingCondition: 'span',
      listingSpecs: '[role="row"]'
    };
  }

  /**
   * Extract data from a search result item
   */
  extractFromSearchResult(element) {
    try {
      const data = {
        type: 'search_result',
        extractedAt: new Date().toISOString(),
        url: window.location.href
      };

      // Get link and URL - look for the main link element
      const linkElement = element.querySelector('a[href*="/marketplace/item/"]');
      if (linkElement) {
        const href = linkElement.getAttribute('href');
        data.url = href ? new URL(href, 'https://www.facebook.com').href : window.location.href;
      }

      // Extract image first
      const imageElement = element.querySelector('img');
      if (imageElement) {
        data.images = [imageElement.src];
        data.thumbnail = imageElement.src;
        // Use alt text as potential title fallback
        if (imageElement.alt) {
          data.altText = imageElement.alt;
        }
      }

      // Extract all text spans and identify price, location
      const textSpans = Array.from(element.querySelectorAll('span[dir="auto"]'));
      const allSpans = Array.from(element.querySelectorAll('span'));

      // Find price spans (contains currency symbols)
      const priceSpans = textSpans.filter(span => {
        const text = span.textContent.trim();
        return /^(CA\$|US\$|\$|€|£|¥|₹)\s*\d+/.test(text) ||
               /^\d+\s*(CA\$|US\$|\$|€|£|¥|₹)/.test(text);
      });

      if (priceSpans.length > 0) {
        // First price is current price
        const currentPriceText = priceSpans[0].textContent.trim();
        data.price = currentPriceText;
        data.currentPrice = currentPriceText;
        data.currency = this.extractCurrency(currentPriceText);
        data.priceNumeric = this.extractNumericPrice(currentPriceText);

        // If there's a second price, it's the original price (marked down)
        if (priceSpans.length > 1) {
          const originalPriceText = priceSpans[1].textContent.trim();
          data.originalPrice = originalPriceText;
          data.originalPriceNumeric = this.extractNumericPrice(originalPriceText);
          data.isMarkdown = true;

          // Calculate discount
          if (data.priceNumeric && data.originalPriceNumeric) {
            data.discountAmount = data.originalPriceNumeric - data.priceNumeric;
            data.discountPercent = Math.round((data.discountAmount / data.originalPriceNumeric) * 100);
          }
        } else {
          data.isMarkdown = false;
        }
      }

      // Find location (usually contains province/state abbreviations or city names)
      // Look for spans with specific location classes or patterns
      const locationSpan = allSpans.find(span => {
        const text = span.textContent.trim();
        const hasLocationClass = span.classList.contains('xlyipyv') || span.classList.contains('xuxw1ft');
        const hasLocationPattern = /\b[A-Z]{2}\b/.test(text) || // Province/state codes like NB, NS, ON
                                  text.includes(',') || // City, Province format
                                  text.includes('miles') || text.includes('km');

        // Exclude price spans from location detection
        const isPrice = /\$/.test(text) || /\d+/.test(text) && text.length < 10;

        return (hasLocationClass || hasLocationPattern) && !isPrice;
      });

      if (locationSpan) {
        data.location = locationSpan.textContent.trim();
      }

      // Find title (usually the longest text span that's not price or location)
      const titleSpan = textSpans.find(span => {
        const text = span.textContent.trim();
        return text.length > 5 &&
               text !== data.price &&
               text !== data.location &&
               !text.includes('CA$') &&
               !text.includes('$');
      });

      if (titleSpan) {
        data.title = titleSpan.textContent.trim();
      } else if (data.altText) {
        // Fallback to alt text if no title found
        data.title = data.altText.split(' in ')[0]; // Remove location from alt text
      }

      // Extract any additional text that might be description or specs
      data.rawText = element.textContent.trim();

      return data;
    } catch (error) {
      console.error('Error extracting search result data:', error);
      return null;
    }
  }

  /**
   * Extract data from individual listing page
   */
  extractFromListingPage() {
    try {
      const data = {
        type: 'listing_page',
        extractedAt: new Date().toISOString(),
        url: window.location.href
      };

      // Extract title
      const titleElement = document.querySelector('h1');
      if (titleElement) {
        data.title = titleElement.textContent.trim();
      }

      // Extract price
      const priceElements = Array.from(document.querySelectorAll('span[dir="auto"]'))
        .filter(el => /[$€£¥₹]/.test(el.textContent));
      if (priceElements.length > 0) {
        const priceText = priceElements[0].textContent.trim();
        data.price = priceText;
        data.currency = this.extractCurrency(priceText);
        data.priceNumeric = this.extractNumericPrice(priceText);
      }

      // Extract location
      const locationElement = document.querySelector('[data-testid*="location"]') ||
                             Array.from(document.querySelectorAll('span')).find(el => 
                               el.textContent.includes('miles') || el.textContent.includes('km'));
      if (locationElement) {
        data.location = locationElement.textContent.trim();
      }

      // Extract seller
      const sellerElements = document.querySelectorAll('strong');
      if (sellerElements.length > 0) {
        data.seller = sellerElements[0].textContent.trim();
      }

      // Extract description
      const descriptionElements = Array.from(document.querySelectorAll('span[dir="auto"]'))
        .filter(el => el.textContent.length > 50);
      if (descriptionElements.length > 0) {
        data.description = descriptionElements[0].textContent.trim();
      }

      // Extract images
      const imageElements = document.querySelectorAll('img');
      data.images = Array.from(imageElements)
        .map(img => img.src)
        .filter(src => src && !src.includes('data:image'))
        .slice(0, 5); // Limit to first 5 images

      if (data.images.length > 0) {
        data.thumbnail = data.images[0];
      }

      // Extract any condition or specs
      const allText = document.body.textContent;
      data.rawText = allText;

      return data;
    } catch (error) {
      console.error('Error extracting listing page data:', error);
      return null;
    }
  }

  /**
   * Extract currency from price text
   */
  extractCurrency(priceText) {
    const currencyMap = {
      '$': 'USD',
      '€': 'EUR',
      '£': 'GBP',
      '¥': 'JPY',
      '₹': 'INR'
    };

    for (const [symbol, code] of Object.entries(currencyMap)) {
      if (priceText.includes(symbol)) {
        return code;
      }
    }
    return 'USD'; // Default
  }

  /**
   * Extract numeric price value
   */
  extractNumericPrice(priceText) {
    const match = priceText.match(/[\d,]+\.?\d*/);
    if (match) {
      return parseFloat(match[0].replace(/,/g, ''));
    }
    return null;
  }

  /**
   * Determine if current page is a search results page
   */
  isSearchResultsPage() {
    return window.location.href.includes('/marketplace/') &&
           (window.location.href.includes('/search') ||
            window.location.href.includes('/category') ||
            document.querySelector('[data-pagelet="MarketplaceSearchResults"]') ||
            document.querySelector('a[href*="/marketplace/item/"]')); // Fallback check
  }

  /**
   * Determine if current page is an individual listing
   */
  isListingPage() {
    return window.location.href.includes('/marketplace/item/') ||
           document.querySelector('[data-pagelet="MarketplaceProductDetailsPageLayout"]');
  }
}

// Make available globally
window.DataExtractor = DataExtractor;
