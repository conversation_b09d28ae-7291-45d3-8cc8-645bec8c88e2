/**
 * Data Extractor - Extracts listing data from Facebook Marketplace pages
 */
class DataExtractor {
  constructor() {
    this.selectors = {
      // Search results page selectors
      searchResult: '[data-pagelet="MarketplaceSearchResults"] [role="main"] > div > div',
      searchTitle: 'span[dir="auto"]',
      searchPrice: 'span[dir="auto"]',
      searchLocation: 'span[color="secondary"]',
      searchImage: 'img',
      searchLink: 'a[role="link"]',
      
      // Individual listing page selectors
      listingTitle: '[data-pagelet="MarketplaceProductDetailsPageLayout"] h1',
      listingPrice: '[data-pagelet="MarketplaceProductDetailsPageLayout"] span[dir="auto"]',
      listingLocation: '[data-testid="marketplace_listing_exact_location"]',
      listingDescription: '[data-pagelet="MarketplaceProductDetailsPageLayout"] [data-pagelet="MarketplaceProductDetailsPageLayout"] span[dir="auto"]',
      listingSeller: '[data-pagelet="MarketplaceProductDetailsPageLayout"] strong',
      listingImages: '[data-pagelet="MarketplaceProductDetailsPageLayout"] img',
      listingCondition: 'span:contains("Condition")',
      listingSpecs: '[data-pagelet="MarketplaceProductDetailsPageLayout"] [role="row"]'
    };
  }

  /**
   * Extract data from a search result item
   */
  extractFromSearchResult(element) {
    try {
      const data = {
        type: 'search_result',
        extractedAt: new Date().toISOString(),
        url: window.location.href
      };

      // Get link and URL
      const linkElement = element.querySelector('a[role="link"]');
      if (linkElement) {
        const href = linkElement.getAttribute('href');
        data.url = href ? new URL(href, 'https://www.facebook.com').href : window.location.href;
      }

      // Extract title
      const titleElements = element.querySelectorAll('span[dir="auto"]');
      if (titleElements.length > 0) {
        data.title = titleElements[0].textContent.trim();
      }

      // Extract price (usually the second span with currency symbol)
      const priceElements = Array.from(element.querySelectorAll('span[dir="auto"]'))
        .filter(el => /[$€£¥₹]/.test(el.textContent) || /\d+/.test(el.textContent));
      if (priceElements.length > 0) {
        const priceText = priceElements[0].textContent.trim();
        data.price = priceText;
        data.currency = this.extractCurrency(priceText);
        data.priceNumeric = this.extractNumericPrice(priceText);
      }

      // Extract location (usually contains location-related text)
      const locationElements = Array.from(element.querySelectorAll('span'))
        .filter(el => el.textContent.includes('miles') || el.textContent.includes('km') || 
                     el.getAttribute('color') === 'secondary');
      if (locationElements.length > 0) {
        data.location = locationElements[0].textContent.trim();
      }

      // Extract image
      const imageElement = element.querySelector('img');
      if (imageElement) {
        data.images = [imageElement.src];
        data.thumbnail = imageElement.src;
      }

      // Extract any additional text that might be description or specs
      const allText = element.textContent;
      data.rawText = allText;

      return data;
    } catch (error) {
      console.error('Error extracting search result data:', error);
      return null;
    }
  }

  /**
   * Extract data from individual listing page
   */
  extractFromListingPage() {
    try {
      const data = {
        type: 'listing_page',
        extractedAt: new Date().toISOString(),
        url: window.location.href
      };

      // Extract title
      const titleElement = document.querySelector('h1');
      if (titleElement) {
        data.title = titleElement.textContent.trim();
      }

      // Extract price
      const priceElements = Array.from(document.querySelectorAll('span[dir="auto"]'))
        .filter(el => /[$€£¥₹]/.test(el.textContent));
      if (priceElements.length > 0) {
        const priceText = priceElements[0].textContent.trim();
        data.price = priceText;
        data.currency = this.extractCurrency(priceText);
        data.priceNumeric = this.extractNumericPrice(priceText);
      }

      // Extract location
      const locationElement = document.querySelector('[data-testid*="location"]') ||
                             Array.from(document.querySelectorAll('span')).find(el => 
                               el.textContent.includes('miles') || el.textContent.includes('km'));
      if (locationElement) {
        data.location = locationElement.textContent.trim();
      }

      // Extract seller
      const sellerElements = document.querySelectorAll('strong');
      if (sellerElements.length > 0) {
        data.seller = sellerElements[0].textContent.trim();
      }

      // Extract description
      const descriptionElements = Array.from(document.querySelectorAll('span[dir="auto"]'))
        .filter(el => el.textContent.length > 50);
      if (descriptionElements.length > 0) {
        data.description = descriptionElements[0].textContent.trim();
      }

      // Extract images
      const imageElements = document.querySelectorAll('img');
      data.images = Array.from(imageElements)
        .map(img => img.src)
        .filter(src => src && !src.includes('data:image'))
        .slice(0, 5); // Limit to first 5 images

      if (data.images.length > 0) {
        data.thumbnail = data.images[0];
      }

      // Extract any condition or specs
      const allText = document.body.textContent;
      data.rawText = allText;

      return data;
    } catch (error) {
      console.error('Error extracting listing page data:', error);
      return null;
    }
  }

  /**
   * Extract currency from price text
   */
  extractCurrency(priceText) {
    const currencyMap = {
      '$': 'USD',
      '€': 'EUR',
      '£': 'GBP',
      '¥': 'JPY',
      '₹': 'INR'
    };

    for (const [symbol, code] of Object.entries(currencyMap)) {
      if (priceText.includes(symbol)) {
        return code;
      }
    }
    return 'USD'; // Default
  }

  /**
   * Extract numeric price value
   */
  extractNumericPrice(priceText) {
    const match = priceText.match(/[\d,]+\.?\d*/);
    if (match) {
      return parseFloat(match[0].replace(/,/g, ''));
    }
    return null;
  }

  /**
   * Determine if current page is a search results page
   */
  isSearchResultsPage() {
    return window.location.href.includes('/marketplace/') && 
           (window.location.href.includes('/search') || 
            window.location.href.includes('/category') ||
            document.querySelector('[data-pagelet="MarketplaceSearchResults"]'));
  }

  /**
   * Determine if current page is an individual listing
   */
  isListingPage() {
    return window.location.href.includes('/marketplace/item/') ||
           document.querySelector('[data-pagelet="MarketplaceProductDetailsPageLayout"]');
  }
}

// Make available globally
window.DataExtractor = DataExtractor;
