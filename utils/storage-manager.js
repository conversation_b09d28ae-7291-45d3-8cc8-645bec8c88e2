/**
 * Storage Manager - Handles Chrome storage operations for selected items
 */
class StorageManager {
  constructor() {
    this.STORAGE_KEY = 'marketplace_selected_items';
    this.MAX_ITEMS = 100;
  }

  /**
   * Get all selected items from storage
   */
  async getSelectedItems() {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEY]);
      return result[this.STORAGE_KEY] || [];
    } catch (error) {
      console.error('Error getting selected items:', error);
      return [];
    }
  }

  /**
   * Add an item to selected items
   */
  async addItem(item) {
    try {
      const items = await this.getSelectedItems();
      
      // Check if item already exists (by URL)
      const existingIndex = items.findIndex(existing => existing.url === item.url);
      if (existingIndex !== -1) {
        return false; // Item already exists
      }

      // Add timestamp
      item.dateSelected = new Date().toISOString();
      
      // Add to beginning of array
      items.unshift(item);
      
      // Limit number of items
      if (items.length > this.MAX_ITEMS) {
        items.splice(this.MAX_ITEMS);
      }

      await chrome.storage.local.set({ [this.STORAGE_KEY]: items });
      return true;
    } catch (error) {
      console.error('Error adding item:', error);
      return false;
    }
  }

  /**
   * Remove an item from selected items
   */
  async removeItem(url) {
    try {
      const items = await this.getSelectedItems();
      const filteredItems = items.filter(item => item.url !== url);
      await chrome.storage.local.set({ [this.STORAGE_KEY]: filteredItems });
      return true;
    } catch (error) {
      console.error('Error removing item:', error);
      return false;
    }
  }

  /**
   * Check if an item is selected
   */
  async isItemSelected(url) {
    try {
      const items = await this.getSelectedItems();
      return items.some(item => item.url === url);
    } catch (error) {
      console.error('Error checking item selection:', error);
      return false;
    }
  }

  /**
   * Clear all selected items
   */
  async clearAllItems() {
    try {
      await chrome.storage.local.set({ [this.STORAGE_KEY]: [] });
      return true;
    } catch (error) {
      console.error('Error clearing items:', error);
      return false;
    }
  }

  /**
   * Get count of selected items
   */
  async getItemCount() {
    try {
      const items = await this.getSelectedItems();
      return items.length;
    } catch (error) {
      console.error('Error getting item count:', error);
      return 0;
    }
  }
}

// Make available globally
window.StorageManager = StorageManager;
